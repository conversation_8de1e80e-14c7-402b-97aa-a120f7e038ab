import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';

interface LeaveRequest {
  id: string;
  employeeName: string;
  employeeEmail: string;
  leaveType: string;
  startDate: Date;
  endDate: Date;
  days: number;
  reason: string;
  status: 'Pending' | 'Approved' | 'Rejected';
  submittedDate: Date;
  approvedBy?: string;
  comments?: string;
}

@Component({
  selector: 'app-leave-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatChipsModule,
    MatTabsModule,
    MatTooltipModule,
    MatMenuModule,
    MatDividerModule
  ],
  template: `
    <div class="leave-management">
      <div class="header">
        <h1>
          <mat-icon>event_available</mat-icon>
          Leave Management
        </h1>
        <p>Manage employee leave requests and balances</p>
      </div>

      <!-- Statistics Cards -->
      <div class="stats-section">
        <div class="stats-grid">
          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-content">
                <div class="stat-icon pending">
                  <mat-icon>pending</mat-icon>
                </div>
                <div class="stat-info">
                  <h3>{{getPendingRequests()}}</h3>
                  <p>Pending Requests</p>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-content">
                <div class="stat-icon approved">
                  <mat-icon>check_circle</mat-icon>
                </div>
                <div class="stat-info">
                  <h3>{{getApprovedRequests()}}</h3>
                  <p>Approved This Month</p>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-content">
                <div class="stat-icon rejected">
                  <mat-icon>cancel</mat-icon>
                </div>
                <div class="stat-info">
                  <h3>{{getRejectedRequests()}}</h3>
                  <p>Rejected</p>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-content">
                <div class="stat-icon total">
                  <mat-icon>calendar_today</mat-icon>
                </div>
                <div class="stat-info">
                  <h3>{{getTotalDays()}}</h3>
                  <p>Total Days Requested</p>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>

      <!-- Filters -->
      <mat-card class="filters-card">
        <mat-card-content>
          <div class="filters-row">
            <mat-form-field appearance="outline">
              <mat-label>Search</mat-label>
              <input matInput [(ngModel)]="searchTerm" (ngModelChange)="applyFilters()" placeholder="Employee name or email">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Leave Type</mat-label>
              <mat-select [(ngModel)]="selectedLeaveType" (selectionChange)="applyFilters()">
                <mat-option value="">All Types</mat-option>
                <mat-option value="Annual Leave">Annual Leave</mat-option>
                <mat-option value="Sick Leave">Sick Leave</mat-option>
                <mat-option value="Personal Leave">Personal Leave</mat-option>
                <mat-option value="Maternity Leave">Maternity Leave</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select [(ngModel)]="selectedStatus" (selectionChange)="applyFilters()">
                <mat-option value="">All Status</mat-option>
                <mat-option value="Pending">Pending</mat-option>
                <mat-option value="Approved">Approved</mat-option>
                <mat-option value="Rejected">Rejected</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Leave Requests Table -->
      <mat-card class="requests-table-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>list</mat-icon>
            Leave Requests ({{filteredRequests.length}})
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="filteredRequests" class="requests-table">
              <!-- Employee Column -->
              <ng-container matColumnDef="employee">
                <th mat-header-cell *matHeaderCellDef>Employee</th>
                <td mat-cell *matCellDef="let request">
                  <div class="employee-info">
                    <strong>{{request.employeeName}}</strong>
                    <small>{{request.employeeEmail}}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Leave Type Column -->
              <ng-container matColumnDef="leaveType">
                <th mat-header-cell *matHeaderCellDef>Leave Type</th>
                <td mat-cell *matCellDef="let request">{{request.leaveType}}</td>
              </ng-container>

              <!-- Dates Column -->
              <ng-container matColumnDef="dates">
                <th mat-header-cell *matHeaderCellDef>Dates</th>
                <td mat-cell *matCellDef="let request">
                  <div class="date-range">
                    <div>{{request.startDate | date:'shortDate'}} - {{request.endDate | date:'shortDate'}}</div>
                    <small>{{request.days}} days</small>
                  </div>
                </td>
              </ng-container>

              <!-- Reason Column -->
              <ng-container matColumnDef="reason">
                <th mat-header-cell *matHeaderCellDef>Reason</th>
                <td mat-cell *matCellDef="let request">
                  <div class="reason-text">{{request.reason}}</div>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let request">
                  <mat-chip [class]="getStatusClass(request.status)">
                    {{request.status}}
                  </mat-chip>
                </td>
              </ng-container>

              <!-- Submitted Date Column -->
              <ng-container matColumnDef="submittedDate">
                <th mat-header-cell *matHeaderCellDef>Submitted</th>
                <td mat-cell *matCellDef="let request">{{request.submittedDate | date:'shortDate'}}</td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let request">
                  <div class="action-buttons">
                    <button mat-icon-button (click)="viewRequest(request)" matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-icon-button 
                            *ngIf="request.status === 'Pending'" 
                            (click)="approveRequest(request)" 
                            matTooltip="Approve">
                      <mat-icon color="primary">check</mat-icon>
                    </button>
                    <button mat-icon-button 
                            *ngIf="request.status === 'Pending'" 
                            (click)="rejectRequest(request)" 
                            matTooltip="Reject">
                      <mat-icon color="warn">close</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .leave-management {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header {
      margin-bottom: 2rem;
      text-align: center;
    }

    .stats-section {
      margin-bottom: 2rem;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.2);
    }

    .stat-info h3 {
      font-size: 2rem;
      margin: 0;
      font-weight: bold;
    }

    .stat-info p {
      margin: 0;
      opacity: 0.9;
    }

    .filters-card {
      margin-bottom: 1.5rem;
    }

    .filters-row {
      display: flex;
      gap: 1rem;
      align-items: center;
      flex-wrap: wrap;
    }

    .filters-row mat-form-field {
      min-width: 200px;
    }

    .requests-table-card {
      margin-bottom: 2rem;
    }

    .table-container {
      overflow-x: auto;
    }

    .requests-table {
      width: 100%;
      min-width: 900px;
    }

    .employee-info {
      display: flex;
      flex-direction: column;
    }

    .employee-info strong {
      font-weight: 500;
      margin-bottom: 0.25rem;
    }

    .employee-info small {
      color: #666;
      font-size: 0.75rem;
    }

    .date-range {
      display: flex;
      flex-direction: column;
    }

    .date-range small {
      color: #666;
      font-size: 0.75rem;
    }

    .reason-text {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .action-buttons {
      display: flex;
      gap: 0.25rem;
    }

    .status-pending {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .status-approved {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .status-rejected {
      background-color: #ffebee;
      color: #c62828;
    }

    @media (max-width: 768px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }

      .filters-row {
        flex-direction: column;
        align-items: stretch;
      }

      .filters-row mat-form-field {
        min-width: auto;
        width: 100%;
      }

      .table-container {
        margin: 0 -1rem;
      }
    }
  `]
})
export class LeaveManagementComponent implements OnInit {
  leaveRequests: LeaveRequest[] = [];
  filteredRequests: LeaveRequest[] = [];
  displayedColumns: string[] = ['employee', 'leaveType', 'dates', 'reason', 'status', 'submittedDate', 'actions'];
  
  searchTerm = '';
  selectedLeaveType = '';
  selectedStatus = '';

  ngOnInit(): void {
    this.loadLeaveRequests();
  }

  loadLeaveRequests(): void {
    // Mock data
    this.leaveRequests = [
      {
        id: '1',
        employeeName: 'John Doe',
        employeeEmail: '<EMAIL>',
        leaveType: 'Annual Leave',
        startDate: new Date('2024-03-15'),
        endDate: new Date('2024-03-20'),
        days: 5,
        reason: 'Family vacation',
        status: 'Pending',
        submittedDate: new Date('2024-03-01')
      },
      {
        id: '2',
        employeeName: 'Sarah Wilson',
        employeeEmail: '<EMAIL>',
        leaveType: 'Sick Leave',
        startDate: new Date('2024-03-10'),
        endDate: new Date('2024-03-12'),
        days: 3,
        reason: 'Medical appointment',
        status: 'Approved',
        submittedDate: new Date('2024-03-08'),
        approvedBy: 'HR Manager'
      },
      {
        id: '3',
        employeeName: 'Mike Johnson',
        employeeEmail: '<EMAIL>',
        leaveType: 'Personal Leave',
        startDate: new Date('2024-03-25'),
        endDate: new Date('2024-03-26'),
        days: 2,
        reason: 'Personal matters',
        status: 'Rejected',
        submittedDate: new Date('2024-03-05'),
        comments: 'Insufficient notice period'
      }
    ];
    this.applyFilters();
  }

  applyFilters(): void {
    this.filteredRequests = this.leaveRequests.filter(request => {
      const matchesSearch = !this.searchTerm || 
        request.employeeName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        request.employeeEmail.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesLeaveType = !this.selectedLeaveType || request.leaveType === this.selectedLeaveType;
      const matchesStatus = !this.selectedStatus || request.status === this.selectedStatus;
      
      return matchesSearch && matchesLeaveType && matchesStatus;
    });
  }

  getStatusClass(status: string): string {
    return `status-${status.toLowerCase()}`;
  }

  getPendingRequests(): number {
    return this.leaveRequests.filter(req => req.status === 'Pending').length;
  }

  getApprovedRequests(): number {
    return this.leaveRequests.filter(req => req.status === 'Approved').length;
  }

  getRejectedRequests(): number {
    return this.leaveRequests.filter(req => req.status === 'Rejected').length;
  }

  getTotalDays(): number {
    return this.leaveRequests.reduce((total, req) => total + req.days, 0);
  }

  viewRequest(request: LeaveRequest): void {
    console.log('View request:', request);
  }

  approveRequest(request: LeaveRequest): void {
    console.log('Approve request:', request);
    request.status = 'Approved';
    request.approvedBy = 'HR Manager';
    this.applyFilters();
  }

  rejectRequest(request: LeaveRequest): void {
    console.log('Reject request:', request);
    request.status = 'Rejected';
    this.applyFilters();
  }
}
